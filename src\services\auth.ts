import axios from 'axios';
import type { AuthResponse, LoginRequest, SignupRequest, UserInfoResponse } from '../types/api';
import { clearTokens, createBaseApi, getRefreshToken, ServiceError, ServiceErrorType, setTokens } from './baseService';

const authAPI = createBaseApi();

// A single promise for token refresh to prevent multiple refresh calls
let tokenRefreshPromise: Promise<string> | null = null;

// Error types for better error handling
export enum AuthErrorType {
    INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
    NETWORK_ERROR = 'NETWORK_ERROR',
    TOKEN_EXPIRED = 'TOKEN_EXPIRED',
    REFRESH_FAILED = 'REFRESH_FAILED',
    UNAUTHORIZED = 'UNAUTHORIZED',
    SERVER_ERROR = 'SERVER_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class AuthError extends Error {
    type: AuthErrorType;
    originalError?: any;

    constructor(type: AuthErrorType, message: string, originalError?: any) {
        super(message);
        this.type = type;
        this.originalError = originalError;
        this.name = 'AuthError';
    }
}

// Auth functions
export const login = async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await authAPI.post<AuthResponse>('/api/login', data);
    setTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
};

export const signup = async (data: SignupRequest): Promise<AuthResponse> => {
    const response = await authAPI.post<AuthResponse>('/api/signup', data);
    setTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
};

export const logout = async (): Promise<void> => {
    const refreshToken = getRefreshToken();
    clearTokens();

    if (!refreshToken) {
        return;
    }

    try {
        await authAPI.post('/api/logout', { refresh_token: refreshToken });
    } catch (error) {
        console.error('Logout error:', error);
    }
};

export const refreshToken = async (): Promise<string> => {
    // If a refresh is already in progress, return the existing promise
    if (tokenRefreshPromise) {
        return tokenRefreshPromise;
    }

    // Create a new refresh token promise
    tokenRefreshPromise = (async () => {
        try {
            // Get the refresh token from storage
            const refreshToken = getRefreshToken();
            if (!refreshToken) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'No refresh token available'
                );
            }

            // Make the refresh token request
            const response = await authAPI.post<AuthResponse>('/api/refresh-token', {
                refresh_token: refreshToken
            });

            // Validate the response
            if (!response.data.access_token || !response.data.refresh_token) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'Invalid token response from server'
                );
            }

            // Store the new tokens
            setTokens(response.data.access_token, response.data.refresh_token);

            // Return the new access token
            return response.data.access_token;
        } catch (error) {
            // Handle specific error cases
            if (error instanceof ServiceError) {
                // If it's already a ServiceError, just rethrow it
                clearTokens();
                throw error;
            } else if (axios.isAxiosError(error)) {
                // Handle network or server errors
                const axiosError = error;
                const status = axiosError.response?.status;

                if (status === 401 || status === 403) {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.UNAUTHORIZED,
                        'Your session has expired. Please sign in again.',
                        error,
                        status
                    );
                } else {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.REFRESH_FAILED,
                        'Failed to refresh authentication. Please sign in again.',
                        error,
                        status
                    );
                }
            } else {
                // Handle unknown errors
                clearTokens();
                throw new ServiceError(
                    ServiceErrorType.UNKNOWN_ERROR,
                    'An unexpected error occurred while refreshing authentication.',
                    error
                );
            }
        } finally {
            // Always clear the promise when done
            tokenRefreshPromise = null;
        }
    })();

    return tokenRefreshPromise;
};

export const getUserInfo = async (): Promise<UserInfoResponse> => {
    const response = await authAPI.get<UserInfoResponse>('/api/user-info');
    return response.data;
};

export const isAuthenticated = (): boolean => {
    return !!getRefreshToken();
};